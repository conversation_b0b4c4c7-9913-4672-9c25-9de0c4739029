import request from '@/utils/request'

/** 科室列表
 * @param {number} pageNum 当前页码
 * @param {number} pageSize 每页显示条数
 * @param {string} departmentName 科室名称
 */
export function department_list({ pageNum, pageSize, departmentName }) {
  return request({
    url: '/department/list',
    method: 'get',
    params: { pageNum, pageSize, departmentName }
  })
}

/** 添加科室
 * @param {string} departmentName 科室名称
 * @param {string} remark 说明
 */
export function department_add({ departmentName, remark }) {
  return request({
    url: '/department/add',
    method: 'post',
    data: { departmentName, remark }
  })
}

/** 修改科室
 * @param {string} departmentId 科室ID
 * @param {string} departmentName 科室名称
 * @param {string} remark 说明
 */
export function department_update({ departmentId, departmentName, remark }) {
  return request({
    url: '/department/update',
    method: 'post',
    data: { departmentId, departmentName, remark }
  })
}

/** 删除科室
 * @param {string} departmentId 科室ID
 */
export function department_delete({ departmentId }) {
  return request({
    url: '/department/remove',
    method: 'DELETE',
    params: { id: departmentId }
  })
}
