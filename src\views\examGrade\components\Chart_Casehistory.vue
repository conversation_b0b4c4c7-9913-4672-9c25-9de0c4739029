<template>
  <div class="Chart_Casehistory" ref="ChartCasehistory"></div>
</template>
<script>
import * as echarts from 'echarts'
import resize from '@/components/Charts/mixins/resize'
export default {
  name: 'ChartCasehistory',
  mixins: [resize],
  data() {
    return {
      chart: null
    }
  },
  created() {},
  mounted() {
    // 基于准备好的dom，初始化echarts实例并应用配置
    this.chart = echarts.init(this.$refs['ChartCasehistory'])
  },
  methods: {
    init(data) {
      const that = this
      const types = ['现病史', '既往史', '个人史', '婚育史', '遗传史', '人文关怀']
      const typeData = types.map((item) => {
        return {
          name: item,
          value: 0
        }
      })
      data.dtos?.forEach((item) => {
        if (types.includes(item.typeName)) {
          typeData.forEach((type) => {
            if (type.name === item.typeName) {
              type.value = item.count
            }
          })
        }
      })
      /** @type EChartsOption */
      var option = {
        color: ['#0091FF', '#13CABC', '#FFC02C', '#FF8D72', '#274e6a', '#5ab11d'],
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            var data = params.data
            return `${data.name}<br/>数量：${data.value}人<br/>占比：${params.percent}%`
          },
          backgroundColor: 'rgba(0, 0, 0, 0.5)', // 提示框背景颜色为黑色的50%透明度
          borderColor: 'transparent',
          borderRadius: 10,
          textStyle: {
            color: '#FFFFFF', // 设置 tooltip 文字颜色为白色
            fontSize: 16
          }
        },
        legend: {
          icon: 'rect',
          left: 'center',
          top: 40,
          itemWidth: 20, // 设置图例项的宽度
          itemHeight: 10, // 设置图例项的高度
          textStyle: {
            color: '#666666',
            fontSize: 14,
            fontFamily: 'PingFang SC'
          }
        },
        series: [
          {
            name: '及格率',
            type: 'pie',
            radius: '160px',
            center: ['50%', '60%'],
            left: 'center',
            data: typeData,
            label: {
              formatter: function (params) {
                // 这里使用ECharts的富文本进行配置
                return `${params.name}:${params.value}个(${params.percent}%) \n \n`
              },
              color: '#999999',
              fontSize: 14
            },
            labelLine: {
              length: 20,
              length2: 0,
              maxSurfaceAngle: 80
            },
            labelLayout: function (params) {
              const isLeft = params.labelRect.x < that.chart.getWidth() / 2
              const points = params.labelLinePoints
              // Update the end point.
              points[2][0] = isLeft ? params.labelRect.x : params.labelRect.x + params.labelRect.width
              return {
                labelLinePoints: points
              }
            }
          }
        ]
      }

      this.chart.setOption(option)
    }
  }
}
</script>
<style scoped lang="scss">
.Chart_Casehistory {
  width: 100%;
  height: 100%;
}
</style>
