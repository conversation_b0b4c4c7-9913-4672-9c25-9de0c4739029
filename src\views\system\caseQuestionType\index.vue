<template>
  <div>
    <div class="div-top">
      <span>问诊类型</span>
    </div>
    <div style="margin: 10px">
      <el-form :model="queryInfo" label-width="80px" inline>
        <el-form-item label="类型名称:">
          <el-input
            v-model="queryInfo.name"
            placeholder="请输入类型名称"
            maxlength="40"
            clearable
            @keydown.native.enter="getList"
            @clear="getList"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getList">搜索</el-button>
          <el-button type="primary" icon="el-icon-circle-plus-outline" @click="addDialog = true">添加问诊类型</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="list" style="width: 100%" border>
        <el-table-column align="center" prop="name" label="类型名称" width="width"> </el-table-column>
        <el-table-column align="center" prop="remark" label="说明" width="width"> </el-table-column>
        <el-table-column align="center" prop="realName" label="创建人" width="width"> </el-table-column>
        <el-table-column align="center" prop="createTime" label="创建时间" width="width"> </el-table-column>
        <el-table-column label="操作" width="width" align="center">
          <template v-slot="{ row }">
            <el-button type="primary" size="small" @click="edit(row)">编辑</el-button>
            <el-button type="danger" size="small" @click="remove(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="width: 100%; margin: 15px; text-align: center">
        <el-pagination
          background
          @current-change="getList"
          @size-change="getList"
          :current-page.sync="queryInfo.pageNum"
          :page-sizes="[5, 10, 20, 40]"
          :page-size.sync="queryInfo.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <!-- 新增、修改问诊类型 -->
    <AddCaseQuestionType ref="AddCaseQuestionType" :addDialog.sync="addDialog" @success="getList" />
  </div>
</template>
<script>
import { caseQuestionTypeList, caseQuestionTypeRemove } from '@/api/caseQuestionType'
import AddCaseQuestionType from './add.vue'
export default {
  components: {
    AddCaseQuestionType
  },
  name: 'caseQuestionType',
  data() {
    return {
      queryInfo: {
        name: null,
        pageNum: 1,
        pageSize: 10
      },
      list: [],
      total: 0,
      addDialog: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      const { data } = await caseQuestionTypeList(this.queryInfo)
      this.list = data.list
      this.total = data.total
    },
    edit(row) {
      this.$refs['AddCaseQuestionType'].form = { ...row }
      this.addDialog = true
    },
    remove(row) {
      this.$confirm('确定删除该类型吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          await caseQuestionTypeRemove({ id: row.typeId })
          this.getList()
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    }
  }
}
</script>
<style scoped lang="scss"></style>
