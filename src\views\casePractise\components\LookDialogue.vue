<template>
  <div>
    <el-dialog custom-class="questionRecord" :visible="showDialog" center title="对话记录" top="5vh" @close="close">
      <div class="tabBarContent">
        <el-tabs v-model="questionType">
          <el-tab-pane v-for="item in types" :key="item" :label="moduleLabel(item)" :name="item">
            <DialogueItem :list="moduleList(item)" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import DialogueItem from '@/views/casePractise/components/DialogueItem'
export default {
  name: 'LookDialogue',
  components: {
    DialogueItem
  },
  props: {
    showDialog: {
      type: Boolean,
      require: true
    },

    record: {
      type: Array,
      default: []
    }
  },

  data() {
    return {
      questionType: '现病史', // 1:重要 2：常规 3：无效
      types: ['现病史', '既往史', '个人史', '婚育史', '遗传史', '人文关怀']
    }
  },
  created() {},
  methods: {
    close() {
      this.questionType = '现病史'
      this.$emit('update:showDialog', false)
    },
    moduleLabel(typeName) {
      let num = 0
      let score = 0
      const dialogue = this.record.filter((item) => {
        return item.typeName === typeName
      })
      num = dialogue.length
      score = dialogue.reduce((accumulator, currentValue) => {
        return accumulator + currentValue.score ? accumulator + parseFloat(currentValue.score) : 0
      }, 0)
      return `${typeName}(${num}个/${score}分)`
    },
    moduleList(typeName) {
      const list = this.record.filter((item) => {
        return item.typeName === typeName
      })
      return list
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .questionRecord {
    width: 1170px;
    height: 800px;
    background: #ffffff;
    border-radius: 30px 30px 30px 30px;
    .el-dialog__header {
      .el-dialog__title {
        font-size: 26px;
        color: #333;
        font-family: PingFang SC;
      }
      .el-dialog__headerbtn {
        .el-dialog__close {
          font-size: 26px;
          height: 26px;
          width: 26px;
        }
      }
    }
    .el-dialog__body {
      display: flex;
      justify-content: center;
      padding-top: 15px;
      padding-left: 30px;
      padding-right: 30px;
    }

    .tabBarContent {
      width: 100%;
      height: 705px;
      margin-top: -1px;
      background: #ffffff;
      border: 1px solid #e0e4e8;
      overflow: auto;
      /* 定义滚动条样式 */
      &::-webkit-scrollbar {
        width: 4px; /* 滚动条宽度 */
        background-color: #f1f1f1; /* 滚动条背景色 */
      }

      /* 定义滚动条轨道样式 */
      &::-webkit-scrollbar-track {
        border-radius: 4px; /* 滚动条轨道的弧形形状 */
      }

      /* 定义滚动条滑块样式 */
      &::-webkit-scrollbar-thumb {
        background-color: #a8a8a8; /* 滚动条滑块颜色 */
        border-radius: 4px; /* 滚动条滑块的弧形形状 */
      }

      /* 定义滚动条滑块在悬停状态时的样式 */
      &::-webkit-scrollbar-thumb:hover {
        background-color: #888787; /* 滚动条滑块悬停状态的颜色 */
      }
      .el-tabs__nav-scroll {
        height: 70px;
        padding-left: 35px;
      }
      .el-tabs__nav-wrap::after {
        height: 1px;
        background: #e0e4e8;
      }
      .el-tabs__item {
        height: 70px;
        line-height: 70px;
        font-size: 18px;
        font-family: PingFang SC;
        font-weight: 500;
        color: #666666;
      }
      .el-tabs__item.is-active {
        color: #3082bd;
      }
    }
  }
}
</style>
