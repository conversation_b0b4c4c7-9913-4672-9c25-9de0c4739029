<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :inline="true" @submit.native.prevent>
      <el-form-item label="科室名称">
        <el-input
          v-model.trim="queryInfo.departmentName"
          placeholder="请输入科室名称"
          clearable
          @keyup.enter.native="handleSearch"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleSearch">查询</el-button>
        <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
      </el-form-item>
      <el-form-item class="pull-right">
        <el-button type="primary" icon="el-icon-plus" @click="openAdd">新增科室</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table :data="list" v-loading="loading" border stripe highlight-current-row style="width: 100%">
      <el-table-column align="center" prop="departmentName" label="科室名称" min-width="160" show-overflow-tooltip />
      <el-table-column align="center" prop="remark" label="说明" min-width="220" show-overflow-tooltip />
      <el-table-column align="center" prop="createTime" label="创建时间" />
      <el-table-column align="center" prop="updateTime" label="更新时间" />
      <el-table-column align="center" label="操作" fixed="right">
        <template #default="{ row }">
          <el-button size="small" type="primary" @click="openEdit(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="department-pagination">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :current-page.sync="queryInfo.pageNum"
        :page-size.sync="queryInfo.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        @current-change="getList"
        @size-change="handleSizeChange"
      />
    </div>
    <!-- 新增/编辑弹窗 -->
    <AddEdit :dialog-visible.sync="dialogVisible" :initial-data="currentRow" @success="getList" />
  </div>
</template>
<script>
import { department_list, department_delete } from '@/api/department'
import AddEdit from './components/Add-Edit.vue'

export default {
  name: 'DepartmentIndex',
  components: { AddEdit },
  data() {
    return {
      // 查询参数
      queryInfo: {
        pageNum: 1,
        pageSize: 10,
        departmentName: null
      },
      loading: false,
      list: [],
      total: 0,
      // 弹窗与当前编辑项
      dialogVisible: false,
      currentRow: null
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 查询列表
    async getList() {
      this.loading = true
      try {
        const { data } = await department_list(this.queryInfo)
        this.list = Array.isArray(data?.list) ? data.list : []
        this.total = Number(data?.total || 0)
      } finally {
        this.loading = false
      }
    },
    // 搜索
    handleSearch() {
      this.queryInfo.pageNum = 1
      this.getList()
    },
    // 重置
    handleReset() {
      this.queryInfo = { pageNum: 1, pageSize: this.queryInfo.pageSize, departmentName: null }
      this.getList()
    },
    // 分页大小变更
    handleSizeChange(size) {
      this.queryInfo.pageSize = size
      this.queryInfo.pageNum = 1
      this.getList()
    },
    // 打开新增
    openAdd() {
      this.currentRow = null
      this.dialogVisible = true
    },
    // 打开编辑
    openEdit(row) {
      this.currentRow = { ...row }
      this.dialogVisible = true
    },
    // 删除
    async handleDelete(row) {
      try {
        await this.$confirm(`确认删除科室【${row.departmentName}】吗？`, '提示', { type: 'warning' })
      } catch (e) {
        return
      }
      this.loading = true
      try {
        await department_delete({ departmentId: row.departmentId })
        this.$message.success('删除成功')
        // 如果当前页数据删空了，回退一页
        if (this.list.length === 1 && this.queryInfo.pageNum > 1) {
          this.queryInfo.pageNum -= 1
        }
        this.getList()
      } finally {
        this.loading = false
      }
    }
  }
}
</script>
<style scoped lang="scss">
.department-pagination {
  margin-top: 12px;
  text-align: right;
}
.pull-right {
  float: right;
}
</style>
