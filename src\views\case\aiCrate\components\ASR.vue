<template>
  <div class="asr-container">
    <el-dialog
      :visible.sync="asrDialogVisible"
      custom-class="asr-dialog"
      width="480px"
      :modal="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="asr-view">
        <div class="asr-title">飞飞在听，请说话</div>
        <div class="asr-wave"></div>
        <div class="asr-text">慢性肺阻病具体都有什么症状，此病例和肺癌什么区别，帮我详情阐述下？</div>
        <div class="asr-button">
          <el-button>结束语音</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: '',
  data() {
    return {
      asrDialogVisible: false
    }
  },
  created() {},
  methods: {}
}
</script>
<style lang="scss">
.asr-container {
  position: relative;
  width: 100%;
  height: 100%;
}
.asr-dialog {
  position: absolute;
  left: unset;
  right: 90px;
  bottom: 68px;

  width: 480px;
  height: 360px;
  margin-right: auto;
  background: #f6f9ff;
  box-shadow: 0px 4px 4px 0px #223b63;
  border-radius: 30px 30px 30px 30px;
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
    .asr-view {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding-top: 35px;
      .asr-title {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 26px;
        color: #112044;
      }
      .asr-button {
        .el-button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 112px;
          height: 45px;
          border: none;
          background: linear-gradient(180deg, #6990ab 0%, #405c71 100%);
          border-radius: 63px 63px 63px 63px;
          font-family: PingFang SC;
          font-weight: 500;
          font-size: 14px;
          color: #ffffff;
        }
      }
    }
  }
  .el-dialog__footer {
    display: none;
  }
}
</style>
