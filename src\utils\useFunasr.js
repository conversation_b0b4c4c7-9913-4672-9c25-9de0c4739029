// Vue2版本的FunASR离线语音文件识别hook
export function useFunasr(url, onMessageHandle) {
  let reconnectAttempts = 0
  const MAX_RECONNECT_ATTEMPTS = 5
  const RECONNECT_DELAY = 3000 // 3秒后重连

  // Vue2中使用普通变量，通过回调更新组件状态
  let state = 'stop'
  let temporaryText = ''
  let confirmedText = '' // 已确认的文字（不会被覆盖）

  const hotwords = JSON.stringify({})
  let speechSocket = null

  function wsStart() {
    if ('WebSocket' in window) {
      connect()
      return 1
    } else {
      alert('当前浏览器不支持 WebSocket')
      return 0
    }
  }

  function connect() {
    speechSocket = new WebSocket(url)
    speechSocket.onopen = onOpen
    speechSocket.onclose = onClose
    speechSocket.onmessage = onMessage
    speechSocket.onerror = onError
  }

  function reconnect() {
    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
      reconnectAttempts++
      console.log(`尝试重连... (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`)
      setTimeout(() => {
        connect()
      }, RECONNECT_DELAY)
    } else {
      console.log('重连次数已达到最大值，停止重连')
    }
  }

  // SOCKET连接中的消息与状态响应
  function onOpen() {
    reconnectAttempts = 0
    state = 'start'

    // 离线文件识别配置
    const request = {
      chunk_size: [5, 10, 5],
      wav_name: 'offline_file',
      is_speaking: true,
      chunk_interval: 10,
      itn: true,
      mode: '2pass-offline', // 改为离线模式
      audio_fs: 16000,
      audio_format: 'wav'
    }

    console.log('发送初始配置:', JSON.stringify(request))
    speechSocket?.send(JSON.stringify(request))
    console.log('连接成功')
  }

  function onClose(e) {
    console.log('连接关闭!', e)
    state = 'stop'

    // 非主动关闭的情况下，尝试重连
    if (e.code !== 1000) {
      reconnect()
    }
  }

  function onMessage(e) {
    try {
      const data = JSON.parse(e.data)

      // 处理语音识别结果
      if (data.stamp_sents) {
        // 校验完成的最终文本 - 将当前临时文本确认并追加到已确认文本
        confirmedText += data.text
        temporaryText = '' // 清空临时文本

        const messageData = {
          text: confirmedText, // 发送完整的已确认文本
          isFinish: data.is_final
        }
        onMessageHandle(messageData)
      } else {
        // 实时识别的临时文本
        temporaryText += data.text

        const messageData = {
          text: confirmedText + temporaryText, // 已确认文本 + 当前临时文本
          isFinish: data.is_final
        }
        onMessageHandle(messageData)
      }
    } catch (error) {
      console.error('解析消息失败：', error)
    }
  }

  function onError(e) {
    console.log('连接错误!', e)
    state = 'stop'
    // 发生错误时尝试重连
    reconnect()
  }

  // 发送音频文件数据块
  function sendFileChunk(chunk) {
    if (speechSocket && speechSocket.readyState === WebSocket.OPEN) {
      speechSocket.send(chunk)
    }
  }

  // 结束音频文件处理
  function endAudioProcessing() {
    const chunk_size = [5, 10, 5]
    const request = {
      chunk_size,
      wav_name: 'offline_file',
      is_speaking: false,
      chunk_interval: 10,
      mode: '2pass-offline'
    }
    speechSocket?.send(JSON.stringify(request))
  }

  // 重置识别状态
  function resetRecognition() {
    // 如果连接已打开
    if (speechSocket && speechSocket.readyState === 1) {
      // 发送结束当前识别会话的信号
      const endRequest = {
        is_speaking: false
      }
      speechSocket.send(JSON.stringify(endRequest))
      console.log('发送结束当前识别会话信号')

      // 短暂延迟后开始新的识别会话
      setTimeout(() => {
        // 开始新的识别会话
        const startRequest = {
          chunk_size: [5, 10, 5],
          hotwords: hotwords,
          wav_name: 'offline_file',
          is_speaking: true,
          chunk_interval: 10,
          itn: true,
          mode: '2pass-offline',
          audio_fs: 16000,
          audio_format: 'wav'
        }
        speechSocket?.send(JSON.stringify(startRequest))
        console.log('发送开始新识别会话信号')
      }, 300) // 300ms延迟，确保结束信号被处理

      return true
    }
    return false
  }

  // 获取当前状态
  function getState() {
    return state
  }

  // 关闭WebSocket连接
  function closeConnection() {
    if (speechSocket) {
      speechSocket.close()
      speechSocket = null
    }
  }

  return {
    wsStart,
    sendFileChunk,
    getState,
    endAudioProcessing,
    resetRecognition,
    closeConnection
  }
}
