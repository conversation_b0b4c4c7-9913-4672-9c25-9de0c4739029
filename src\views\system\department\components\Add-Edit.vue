<template>
  <div class="app-container">
    <el-dialog
      :title="isEdit ? '编辑科室' : '新增科室'"
      :visible.sync="visibleProxy"
      width="520px"
      :close-on-click-modal="false"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="82px">
        <el-form-item label="科室名称" prop="departmentName">
          <el-input v-model.trim="form.departmentName" maxlength="50" show-word-limit placeholder="请输入科室名称" />
        </el-form-item>
        <el-form-item label="说明" prop="remark">
          <el-input type="textarea" :rows="3" v-model.trim="form.remark" maxlength="200" show-word-limit placeholder="可填写说明" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" :loading="saving" @click="handleSubmit">保 存</el-button>
      </span>
    </el-dialog>
  </div>
  
</template>
<script>
import { department_add, department_update } from '@/api/department'

export default {
  name: 'DepartmentAddEdit',
  props: {
    // 弹窗可见性（父组件通过 .sync 控制）
    dialogVisible: { type: Boolean, required: true },
    // 初始数据：有 departmentId 则为编辑
    initialData: { type: Object, default: null }
  },
  data() {
    return {
      saving: false,
      form: {
        departmentId: null,
        departmentName: '',
        remark: ''
      },
      rules: {
        departmentName: [
          { required: true, message: '请输入科室名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度为 1-50 字符', trigger: 'blur' }
        ],
        remark: [{ max: 200, message: '最多 200 字符', trigger: 'blur' }]
      }
    }
  },
  computed: {
    // .sync 代理：读 prop，写时向父发射 update 事件
    visibleProxy: {
      get() {
        return this.dialogVisible
      },
      set(val) {
        this.$emit('update:dialogVisible', val)
      }
    },
    isEdit() {
      return !!this.form.departmentId
    }
  },
  watch: {
    // 弹窗打开时，根据 initialData 初始化表单
    dialogVisible(val) {
      if (val) {
        this.initForm()
      }
    },
    initialData: {
      deep: true,
      handler() {
        if (this.dialogVisible) this.initForm()
      }
    }
  },
  methods: {
    // 初始化/重置表单
    initForm() {
      const d = this.initialData || {}
      this.form = {
        departmentId: d.departmentId || null,
        departmentName: d.departmentName || '',
        remark: d.remark || ''
      }
      this.$nextTick(() => this.$refs.formRef && this.$refs.formRef.clearValidate())
    },
    // 取消
    handleCancel() {
      this.visibleProxy = false
    },
    // 提交
    handleSubmit() {
      this.$refs.formRef.validate(async valid => {
        if (!valid) return
        this.saving = true
        try {
          const payload = {
            departmentId: this.form.departmentId,
            departmentName: this.form.departmentName,
            remark: this.form.remark
          }
          if (this.isEdit) {
            await department_update(payload)
            this.$message.success('修改成功')
          } else {
            await department_add(payload)
            this.$message.success('新增成功')
          }
          this.$emit('success')
          this.visibleProxy = false
        } finally {
          this.saving = false
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.dialog-footer {
  display: inline-block;
}
</style>
