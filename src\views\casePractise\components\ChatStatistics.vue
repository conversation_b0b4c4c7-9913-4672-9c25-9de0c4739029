<template>
  <div class="chart-container">
    <!-- 饼图 -->
    <div class="chart-item">
      <div ref="pieChart" class="chart"></div>
    </div>
    <!-- 线性图 -->
    <div class="chart-item">
      <div ref="lineChart" class="chart"></div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import resize from '@/components/Charts/mixins/resize'
import { arabicToChinese } from '@/utils'
export default {
  name: '',
  mixins: [resize],
  data() {
    return {
      pieChart: null,
      lineChart: null
    }
  },
  created() {},
  methods: {
    arabicToChinese,
    init(dtos, scores) {
      this.initPieChart(dtos)
      this.initLineChart(scores)
    },
    // 初始化饼图
    initPieChart(dtos) {
      const that = this
      this.pieChart = echarts.init(this.$refs['pieChart'])
      const pieOption = {
        title: {
          text: '近十次各个问题类型个数及占比',
          left: 'left',
          top: 20,
          left: 20,
          textStyle: {
            color: '#293543',
            fontSize: 26,
            fontFamily: 'PingFang SC',
            fontWeight: 400
          }
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0,0,0,0.5)',
          textStyle: {
            color: '#fff'
          },
          formatter: '{b}: {c}个 ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 30,
          top: 80,
          show: false
        },
        color: ['#0091FF', '#13CABC', '#FFC02C', '#FF8D72', '#8B5CF6', '#F59E0B'],
        series: [
          {
            type: 'pie',
            radius: '45%',
            center: ['50%', '55%'],
            emphasis: {
              focus: 'self'
            },
            data: dtos.map((item) => {
              return {
                name: item.typeName,
                value: item.count
              }
            }),
            label: {
              formatter: '{b}: {c}个 ({d}%)\n\n',
              color: '#999999',
              fontSize: 14
            },
            labelLine: {
              length: 20,
              length2: 0,
              maxSurfaceAngle: 80
            },
            labelLayout: function (params) {
              const isLeft = params.labelRect.x < that.pieChart.getWidth() / 2
              const points = params.labelLinePoints
              // Update the end point.
              points[2][0] = isLeft ? params.labelRect.x : params.labelRect.x + params.labelRect.width
              return {
                labelLinePoints: points
              }
            }
          }
        ]
      }
      this.pieChart.setOption(pieOption)
    },
    // 初始化线性图
    initLineChart(scores) {
      this.lineChart = echarts.init(this.$refs['lineChart'])
      /** @type EChartsOption */
      const lineOption = {
        title: {
          text: '近十次得分趋势图',
          left: 'left',
          top: 27,
          left: 20,
          textStyle: {
            color: '#293543',
            fontSize: 26,
            fontFamily: 'PingFang SC',
            fontWeight: 400
          }
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0,0,0,0.5)',
          textStyle: {
            color: '#fff'
          }
        },
        xAxis: {
          type: 'category',
          axisTick: { show: false },
          axisLabel: {
            padding: [10, 0, 0, 0]
          },
          data: scores.map((item) => item.time)
        },
        yAxis: {
          name: '分数',
          nameTextStyle: {
            padding: [0, 30, 10, 0]
          }
        },
        grid: {
          left: '5%',
          bottom: '10%',
          right: '3%',
          top: '22%'
        },
        color: ['#0091FF'],
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0,0,0,0.7)',
          textStyle: {
            color: '#fff'
          },
          formatter: `{b} <br/> 得分：{c}`
        },
        series: [
          {
            type: 'line',
            smooth: true,
            symbolSize: 8,
            data: scores.map((item) => item.score),
            emphasis: { focus: 'series' },
            lineStyle: {
              width: 3
            }
            // areaStyle: {
            //   opacity: 0.1
            // }
          }
        ]
      }
      this.lineChart.setOption(lineOption)
    }
  }
}
</script>
<style scoped lang="scss">
.chart-container {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 20px;
}

.chart-item {
  flex: 1;
  height: 100%;
}
.chart-item:last-of-type {
  flex: 2;
}

.chart {
  width: 100%;
  height: 100%;
}
</style>
